-- Test search query to debug "Clean Code" search issue
USE LibraryManagementSystem;

-- Show all books in the database
SELECT 
    BookId,
    Title,
    Author,
    ISBN,
    Genre,
    TotalCopies,
    AvailableCopies
FROM Books
ORDER BY Title;

-- Test the search query that would be generated for "Clean Code"
DECLARE @SearchTerm NVARCHAR(100) = '%Clean Code%';

SELECT 
    BookId,
    Title,
    Author,
    ISBN,
    Genre,
    TotalCopies,
    AvailableCopies,
    'Matched by Title' as MatchReason
FROM Books
WHERE Title LIKE @SearchTerm

UNION ALL

SELECT 
    BookId,
    Title,
    Author,
    ISBN,
    Genre,
    TotalCopies,
    AvailableCopies,
    'Matched by Author' as MatchReason
FROM Books
WHERE Author LIKE @SearchTerm

UNION ALL

SELECT 
    BookId,
    Title,
    Author,
    ISBN,
    Genre,
    TotalCopies,
    AvailableCopies,
    'Matched by ISBN' as MatchReason
FROM Books
WHERE ISBN LIKE @SearchTerm;

-- Test what books contain "Code"
SELECT 
    BookId,
    Title,
    Author,
    ISBN,
    'Contains Code in Title' as MatchType
FROM Books
WHERE Title LIKE '%Code%'

UNION ALL

SELECT 
    BookId,
    Title,
    Author,
    ISBN,
    'Contains Code in Author' as MatchType
FROM Books
WHERE Author LIKE '%Code%'

UNION ALL

SELECT 
    BookId,
    Title,
    Author,
    ISBN,
    'Contains Code in ISBN' as MatchType
FROM Books
WHERE ISBN LIKE '%Code%';

-- Test what books contain "Clean"
SELECT 
    BookId,
    Title,
    Author,
    ISBN,
    'Contains Clean in Title' as MatchType
FROM Books
WHERE Title LIKE '%Clean%'

UNION ALL

SELECT 
    BookId,
    Title,
    Author,
    ISBN,
    'Contains Clean in Author' as MatchType
FROM Books
WHERE Author LIKE '%Clean%'

UNION ALL

SELECT 
    BookId,
    Title,
    Author,
    ISBN,
    'Contains Clean in ISBN' as MatchType
FROM Books
WHERE ISBN LIKE '%Clean%';
